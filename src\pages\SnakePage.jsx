import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SnakeGame from '../components/SnakeGame';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/app.css';
import '../styles/game-page.css';

const SnakePage = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (page) => {
    navigate(`/${page}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="game-page-container">
        <div className="game-header">
          <button
            className="back-button"
            onClick={() => navigate('/classic-games')}
          >
            <ArrowBackIcon /> Back to Games
          </button>
          <h1 className="game-title">🐍 Snake</h1>
          <p className="game-description">
            Guide the snake to eat food and grow longer without hitting the walls or yourself! 
            Use arrow keys or WASD on desktop, or use the on-screen controls and swipe gestures on mobile. 
            The classic arcade game that never gets old - how long can you make your snake?
          </p>
        </div>

        <div className="game-container">
          <SnakeGame />
        </div>

        <div className="game-instructions">
          <h2>How to Play</h2>
          <div className="instructions-grid">
            <div className="instruction-item">
              <h3>🎯 Objective</h3>
              <p>Eat the red food to grow your snake and increase your score. Each food gives you 10 points!</p>
            </div>
            <div className="instruction-item">
              <h3>🎮 Desktop Controls</h3>
              <p>Use arrow keys or WASD to change direction. The snake moves continuously in the current direction.</p>
            </div>
            <div className="instruction-item">
              <h3>📱 Mobile Controls</h3>
              <p>Use the on-screen directional pad or swipe on the game area to change direction.</p>
            </div>
            <div className="instruction-item">
              <h3>⚠️ Avoid</h3>
              <p>Don't hit the walls or run into your own body, or the game will end!</p>
            </div>
            <div className="instruction-item">
              <h3>🏆 Strategy</h3>
              <p>Plan your moves carefully as you grow longer. Use the edges wisely and don't trap yourself!</p>
            </div>
            <div className="instruction-item">
              <h3>💾 High Score</h3>
              <p>Your best score is automatically saved locally. Try to beat your personal record!</p>
            </div>
          </div>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default SnakePage;
