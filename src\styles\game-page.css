.game-page-container {
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  text-align: center;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.game-header {
  margin-bottom: 2rem;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--text-light);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  font-family: "<PERSON><PERSON>", sans-serif;
}

.back-button:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3);
}

.dark-mode .back-button {
  background: var(--text-dark);
  color: var(--bg-dark);
}

.dark-mode .back-button:hover {
  background: #90caf9;
  box-shadow: 0 4px 12px rgba(227, 242, 253, 0.3);
}

.game-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-light);
  font-family: "Josefin Sans", sans-serif;
}

.dark-mode .game-title {
  color: var(--text-dark);
}

.game-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #666;
  font-weight: 400;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.dark-mode .game-description {
  color: #aaa;
}

.game-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  overflow: visible;
  padding: 1rem;
  touch-action: manipulation;
  min-height: 500px;
  /* Ensure enough space for the game */
}

.game-container canvas {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  max-width: 100%;
  height: auto;
}

.game-instructions {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
  text-align: left;
}

.dark-mode .game-instructions {
  background: #2d2d2d;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.game-instructions h2 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  text-align: center;
}

.dark-mode .game-instructions h2 {
  color: var(--text-dark);
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.instruction-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid #e9ecef;
}

.dark-mode .instruction-item {
  background: #3a3a3a;
  border-color: #555;
}

.instruction-item h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: var(--text-light);
}

.dark-mode .instruction-item h3 {
  color: var(--text-dark);
}

.instruction-item p {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #666;
  margin: 0;
}

.dark-mode .instruction-item p {
  color: #ccc;
}

.game-instructions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.game-instructions li {
  padding: 0.5rem 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.dark-mode .game-instructions li {
  color: #aaa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-page-container {
    padding: 1rem;
    max-width: 100%;
  }

  .game-title {
    font-size: 2.5rem;
  }

  .game-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .game-container {
    overflow: visible;
    padding: 0.5rem;
    margin-bottom: 1rem;
    min-height: 350px;
  }

  .game-instructions {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .game-instructions h2 {
    font-size: 1.5rem;
  }

  .instructions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .instruction-item {
    padding: 1rem;
  }

  .instruction-item h3 {
    font-size: 1.1rem;
  }

  .game-instructions li {
    font-size: 0.9rem;
  }

  .back-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .game-page-container {
    padding: 0.5rem;
    max-width: 100%;
  }

  .game-title {
    font-size: 2rem;
  }

  .game-description {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }

  .game-container {
    padding: 0.25rem;
    margin-bottom: 0.5rem;
    min-height: 300px;
  }

  .game-instructions {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .game-instructions h2 {
    font-size: 1.4rem;
  }

  .instruction-item {
    padding: 0.8rem;
  }

  .instruction-item h3 {
    font-size: 1rem;
  }

  .game-instructions li {
    font-size: 0.85rem;
    padding: 0.4rem 0;
  }

  .back-button {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}