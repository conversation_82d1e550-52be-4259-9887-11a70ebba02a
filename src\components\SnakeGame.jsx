import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Stage, Layer, Rect, Circle, Text } from 'react-konva';

const SnakeGame = () => {
  // Game constants
  const GRID_SIZE = 20;
  const GAME_WIDTH = 600;
  const GAME_HEIGHT = 400;
  const INITIAL_SNAKE = [{ x: 10, y: 10 }];
  const INITIAL_DIRECTION = { x: 1, y: 0 };
  const GAME_SPEED = 150; // milliseconds

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  // Game state
  const [snake, setSnake] = useState(INITIAL_SNAKE);
  const [direction, setDirection] = useState(INITIAL_DIRECTION);
  const [food, setFood] = useState({ x: 15, y: 15 });
  const [gameState, setGameState] = useState('waiting'); // 'waiting', 'playing', 'gameOver'
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(0);

  // Touch controls
  const [touchStart, setTouchStart] = useState(null);
  const [showControls, setShowControls] = useState(false);

  // Refs
  const gameLoopRef = useRef();
  const directionRef = useRef(INITIAL_DIRECTION);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
      setShowControls(window.innerWidth <= 768 || 'ontouchstart' in window);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load high score from localStorage
  useEffect(() => {
    const savedHighScore = localStorage.getItem('snakeHighScore');
    if (savedHighScore) {
      setHighScore(parseInt(savedHighScore));
    }
  }, []);

  // Generate random food position
  const generateFood = useCallback((currentSnake) => {
    let newFood;
    do {
      newFood = {
        x: Math.floor(Math.random() * (GAME_WIDTH / GRID_SIZE)),
        y: Math.floor(Math.random() * (GAME_HEIGHT / GRID_SIZE))
      };
    } while (currentSnake.some(segment => segment.x === newFood.x && segment.y === newFood.y));
    return newFood;
  }, []);

  // Check collision with walls or self
  const checkCollision = useCallback((head, snakeBody) => {
    // Wall collision
    if (head.x < 0 || head.x >= GAME_WIDTH / GRID_SIZE ||
      head.y < 0 || head.y >= GAME_HEIGHT / GRID_SIZE) {
      return true;
    }

    // Self collision
    return snakeBody.some(segment => segment.x === head.x && segment.y === head.y);
  }, []);

  // Game loop
  const gameLoop = useCallback(() => {
    setSnake(currentSnake => {
      const newSnake = [...currentSnake];
      const head = { ...newSnake[0] };

      // Move head
      head.x += directionRef.current.x;
      head.y += directionRef.current.y;

      // Check collision
      if (checkCollision(head, newSnake)) {
        setGameState('gameOver');
        return currentSnake;
      }

      newSnake.unshift(head);

      // Check food collision
      if (head.x === food.x && head.y === food.y) {
        setScore(prev => prev + 10);
        setFood(generateFood(newSnake));
      } else {
        newSnake.pop();
      }

      return newSnake;
    });
  }, [food, checkCollision, generateFood]);

  // Start game loop
  useEffect(() => {
    if (gameState === 'playing') {
      gameLoopRef.current = setInterval(gameLoop, GAME_SPEED);
    } else {
      clearInterval(gameLoopRef.current);
    }

    return () => clearInterval(gameLoopRef.current);
  }, [gameState, gameLoop]);

  // Handle direction change
  const changeDirection = useCallback((newDirection) => {
    // Prevent reverse direction
    if (directionRef.current.x === -newDirection.x && directionRef.current.y === -newDirection.y) {
      return;
    }

    directionRef.current = newDirection;
    setDirection(newDirection);
  }, []);

  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (gameState !== 'playing') return;

      switch (e.key) {
        case 'ArrowUp':
        case 'w':
        case 'W':
          e.preventDefault();
          changeDirection({ x: 0, y: -1 });
          break;
        case 'ArrowDown':
        case 's':
        case 'S':
          e.preventDefault();
          changeDirection({ x: 0, y: 1 });
          break;
        case 'ArrowLeft':
        case 'a':
        case 'A':
          e.preventDefault();
          changeDirection({ x: -1, y: 0 });
          break;
        case 'ArrowRight':
        case 'd':
        case 'D':
          e.preventDefault();
          changeDirection({ x: 1, y: 0 });
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [gameState, changeDirection]);

  // Touch/Swipe controls
  const handleTouchStart = (e) => {
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
  };

  const handleTouchEnd = (e) => {
    if (!touchStart || gameState !== 'playing') return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    const minSwipeDistance = 50;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Horizontal swipe
      if (Math.abs(deltaX) > minSwipeDistance) {
        changeDirection({ x: deltaX > 0 ? 1 : -1, y: 0 });
      }
    } else {
      // Vertical swipe
      if (Math.abs(deltaY) > minSwipeDistance) {
        changeDirection({ x: 0, y: deltaY > 0 ? 1 : -1 });
      }
    }

    setTouchStart(null);
  };

  // Control button handlers
  const handleControlPress = (newDirection) => {
    if (gameState === 'playing') {
      changeDirection(newDirection);
    }
  };

  // Game control functions
  const startGame = () => {
    setSnake(INITIAL_SNAKE);
    setDirection(INITIAL_DIRECTION);
    directionRef.current = INITIAL_DIRECTION;
    setFood(generateFood(INITIAL_SNAKE));
    setScore(0);
    setGameState('playing');
  };

  const resetGame = () => {
    // Update high score
    if (score > highScore) {
      setHighScore(score);
      localStorage.setItem('snakeHighScore', score.toString());
    }

    setSnake(INITIAL_SNAKE);
    setDirection(INITIAL_DIRECTION);
    directionRef.current = INITIAL_DIRECTION;
    setFood({ x: 15, y: 15 });
    setScore(0);
    setGameState('waiting');
  };

  // Click handler
  const handleClick = () => {
    if (gameState === 'waiting') {
      startGame();
    } else if (gameState === 'gameOver') {
      resetGame();
    }
  };

  // Responsive sizing
  const gameWidth = isMobile ? Math.min(GAME_WIDTH, window.innerWidth - 40) : GAME_WIDTH;
  const gameHeight = isMobile ? Math.min(GAME_HEIGHT, window.innerHeight * 0.5) : GAME_HEIGHT;
  const scale = Math.min(gameWidth / GAME_WIDTH, gameHeight / GAME_HEIGHT);

  return (
    <div style={{ textAlign: 'center', padding: isMobile ? '10px' : '20px' }}>
      {/* Game Stats */}
      <div style={{
        marginBottom: '10px',
        fontSize: isMobile ? '14px' : '16px',
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: isMobile ? '10px' : '20px'
      }}>
        <span>Score: {score}</span>
        <span>High Score: {highScore}</span>
        <span>Length: {snake.length}</span>
      </div>

      {/* Game Canvas */}
      <div style={{
        display: 'inline-block',
        border: '2px solid #333',
        borderRadius: '8px',
        backgroundColor: '#000',
        transform: `scale(${scale})`,
        transformOrigin: 'center'
      }}>
        <Stage
          width={GAME_WIDTH}
          height={GAME_HEIGHT}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          onClick={handleClick}
          style={{ cursor: gameState === 'waiting' || gameState === 'gameOver' ? 'pointer' : 'default' }}
        >
          <Layer>
            {/* Game Background */}
            <Rect
              x={0}
              y={0}
              width={GAME_WIDTH}
              height={GAME_HEIGHT}
              fill="#000"
            />

            {/* Snake */}
            {snake.map((segment, index) => (
              <Rect
                key={index}
                x={segment.x * GRID_SIZE}
                y={segment.y * GRID_SIZE}
                width={GRID_SIZE - 1}
                height={GRID_SIZE - 1}
                fill={index === 0 ? '#4CAF50' : '#8BC34A'} // Head is darker green
                cornerRadius={2}
              />
            ))}

            {/* Food */}
            <Circle
              x={food.x * GRID_SIZE + GRID_SIZE / 2}
              y={food.y * GRID_SIZE + GRID_SIZE / 2}
              radius={GRID_SIZE / 2 - 2}
              fill="#FF5722"
            />

            {/* Game Messages */}
            {gameState === 'waiting' && (
              <Text
                x={GAME_WIDTH / 2}
                y={GAME_HEIGHT / 2 - 40}
                text={`🐍 Snake Game\n\n${isMobile ? 'Use controls below or swipe to move' : 'Use arrow keys or WASD to move'}\n${isMobile ? 'Tap' : 'Click'} to start`}
                fontSize={isMobile ? 16 : 20}
                fill="#fff"
                align="center"
                offsetX={isMobile ? 100 : 120}
              />
            )}

            {gameState === 'gameOver' && (
              <Text
                x={GAME_WIDTH / 2}
                y={GAME_HEIGHT / 2 - 40}
                text={`💀 Game Over!\n\nScore: ${score}\nLength: ${snake.length}\n\n${isMobile ? 'Tap' : 'Click'} to play again`}
                fontSize={isMobile ? 16 : 20}
                fill="#FF5722"
                align="center"
                offsetX={isMobile ? 80 : 100}
              />
            )}
          </Layer>
        </Stage>
      </div>

      {/* Mobile Controls */}
      {showControls && (
        <div style={{
          marginTop: '20px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '10px'
        }}>
          <div style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
            Use controls below or swipe on the game area
          </div>

          {/* Control Pad */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 60px)',
            gridTemplateRows: 'repeat(3, 60px)',
            gap: '5px'
          }}>
            {/* Top row */}
            <div></div>
            <button
              style={{
                width: '60px',
                height: '60px',
                border: '2px solid #333',
                borderRadius: '8px',
                backgroundColor: '#f0f0f0',
                fontSize: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                touchAction: 'manipulation'
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                handleControlPress({ x: 0, y: -1 });
              }}
              onClick={() => handleControlPress({ x: 0, y: -1 })}
              disabled={gameState !== 'playing'}
            >
              ↑
            </button>
            <div></div>

            {/* Middle row */}
            <button
              style={{
                width: '60px',
                height: '60px',
                border: '2px solid #333',
                borderRadius: '8px',
                backgroundColor: '#f0f0f0',
                fontSize: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                touchAction: 'manipulation'
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                handleControlPress({ x: -1, y: 0 });
              }}
              onClick={() => handleControlPress({ x: -1, y: 0 })}
              disabled={gameState !== 'playing'}
            >
              ←
            </button>
            <div style={{
              width: '60px',
              height: '60px',
              border: '2px solid #666',
              borderRadius: '8px',
              backgroundColor: '#e0e0e0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '20px'
            }}>
              🐍
            </div>
            <button
              style={{
                width: '60px',
                height: '60px',
                border: '2px solid #333',
                borderRadius: '8px',
                backgroundColor: '#f0f0f0',
                fontSize: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                touchAction: 'manipulation'
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                handleControlPress({ x: 1, y: 0 });
              }}
              onClick={() => handleControlPress({ x: 1, y: 0 })}
              disabled={gameState !== 'playing'}
            >
              →
            </button>

            {/* Bottom row */}
            <div></div>
            <button
              style={{
                width: '60px',
                height: '60px',
                border: '2px solid #333',
                borderRadius: '8px',
                backgroundColor: '#f0f0f0',
                fontSize: '24px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                userSelect: 'none',
                touchAction: 'manipulation'
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                handleControlPress({ x: 0, y: 1 });
              }}
              onClick={() => handleControlPress({ x: 0, y: 1 })}
              disabled={gameState !== 'playing'}
            >
              ↓
            </button>
            <div></div>
          </div>

          {/* Game Control Buttons */}
          <div style={{ marginTop: '20px', display: 'flex', gap: '10px' }}>
            {gameState === 'waiting' && (
              <button
                style={{
                  padding: '12px 24px',
                  fontSize: '16px',
                  backgroundColor: '#4CAF50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  touchAction: 'manipulation'
                }}
                onClick={startGame}
              >
                Start Game
              </button>
            )}

            {gameState === 'gameOver' && (
              <button
                style={{
                  padding: '12px 24px',
                  fontSize: '16px',
                  backgroundColor: '#FF5722',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  touchAction: 'manipulation'
                }}
                onClick={resetGame}
              >
                Play Again
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SnakeGame;
